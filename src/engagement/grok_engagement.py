#!/usr/bin/env python3
"""
Grok Engagement Farming System for SME Analytica
Strategically asks @grok questions about business insights to generate engagement
"""

import logging
import random
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ..social.twitter_manager import TwitterManager
from ..notion.notion_manager import NotionManager
from ..ai_providers import AIProviderManager, ContentRequest, ContentType


@dataclass
class GrokQuestion:
    """Represents a strategic question to ask Grok"""
    question: str
    category: str  # 'restaurant_analytics', 'sme_insights', 'data_trends', etc.
    expected_engagement: str  # 'high', 'medium', 'low'
    follow_up_ready: bool  # Whether we have follow-up insights to add
    hashtags: List[str]
    target_audience: str


class GrokEngagementFarmer:
    """Farms engagement by asking strategic questions to @grok on Twitter"""
    
    def __init__(self, twitter_manager: TwitterManager):
        self.twitter_manager = twitter_manager
        # Initialize AI manager with basic config
        import os
        ai_config = {
            "openai_api_key": os.getenv("OPENAI_API_KEY", ""),
            "anthropic_api_key": os.getenv("ANTHROPIC_API_KEY", ""),
            "perplexity_api_key": os.getenv("PERPLEXITY_API_KEY", ""),
            "grok_api_key": os.getenv("GROK_API_KEY", ""),
            "google_gemini_api_key": os.getenv("GOOGLE_GEMINI_API_KEY", "")
        }
        self.ai_manager = AIProviderManager(ai_config)
        self.notion_manager = NotionManager()
        self.logger = logging.getLogger(__name__)

        # Engagement tracking
        self.daily_grok_questions = 0
        self.daily_grok_limit = 5  # Increased for more flexibility
        self.last_grok_question_time = None
        self.min_interval_between_questions = 1800  # 30 minutes between questions (more flexible)
        
        # Question categories and templates
        self.question_categories = {
            'restaurant_analytics': {
                'topics': [
                    'menu pricing optimization', 'food cost analysis', 'customer flow patterns',
                    'peak hour staffing', 'seasonal demand forecasting', 'inventory turnover',
                    'profit margin analysis', 'delivery vs dine-in profitability'
                ],
                'hashtags': ['#RestaurantAnalytics', '#FoodBusiness', '#RestaurantTech'],
                'audience': 'restaurant owners and managers'
            },
            'sme_insights': {
                'topics': [
                    'small business data analytics', 'SME growth strategies', 'business intelligence for SMEs',
                    'cash flow optimization', 'customer retention analytics', 'operational efficiency',
                    'competitive analysis', 'market trend analysis'
                ],
                'hashtags': ['#SmallBusiness', '#SMEAnalytics', '#BusinessIntelligence'],
                'audience': 'small business owners'
            },
            'hospitality_tech': {
                'topics': [
                    'hotel revenue management', 'guest experience analytics', 'booking pattern analysis',
                    'seasonal pricing strategies', 'occupancy optimization', 'service quality metrics'
                ],
                'hashtags': ['#HospitalityTech', '#HotelAnalytics', '#TravelTech'],
                'audience': 'hospitality professionals'
            },
            'data_trends': {
                'topics': [
                    'AI in business analytics', 'predictive analytics trends', 'real-time data insights',
                    'automation in small business', 'data-driven decision making', 'business forecasting'
                ],
                'hashtags': ['#DataAnalytics', '#AIforBusiness', '#PredictiveAnalytics'],
                'audience': 'business analysts and decision makers'
            }
        }
        
        # AI-driven question generation - no hardcoded questions
        # All questions will be dynamically generated by AI for maximum relevance and engagement
        self.strategic_questions = []  # Fallback only, primary source is AI generation

        # SME Analytica business context for AI generation
        self.sme_context = {
            "company": "SME Analytica",
            "mission": "Making enterprise-level business intelligence accessible to SMEs",
            "solutions": {
                "MenuFlow": "AI-powered dynamic pricing and menu optimization for restaurants",
                "FlowAnalytics": "Customer flow and behavior analysis for retail and hospitality",
                "DataDriven": "Real-time analytics and predictive insights for SMEs",
                "CostOptimizer": "Inventory and cost management with AI recommendations"
            },
            "target_markets": ["restaurants", "hotels", "retail stores", "cafes", "bars"],
            "value_props": [
                "10%+ margin improvements through dynamic pricing",
                "Real-time analytics without technical complexity",
                "AI-powered insights for non-technical business owners",
                "Enterprise-level BI at SME-friendly prices"
            ],
            "expertise_areas": [
                "menu optimization", "dynamic pricing", "customer flow analysis",
                "inventory management", "predictive analytics", "business intelligence",
                "cost reduction", "revenue optimization", "data-driven decision making"
            ]
        }

    async def run_grok_engagement_farming(self) -> Dict[str, Any]:
        """Run the Grok engagement farming workflow"""
        
        self.logger.info("🤖 Starting Grok Engagement Farming")
        
        results = {
            "grok_farming_mode": "active",
            "questions_asked": 0,
            "follow_ups_posted": 0,
            "engagement_generated": 0,
            "topics_covered": [],
            "errors": []
        }
        
        try:
            # Check if we can ask Grok questions today
            if not self._can_ask_grok_question():
                self.logger.info("Daily Grok question limit reached or too soon since last question")
                return results
            
            # Select a strategic question
            question = await self._select_strategic_question()
            if not question:
                self.logger.warning("No suitable Grok question available")
                return results

            # Ask the question to Grok
            question_result = await self._ask_grok_question(question)
            if question_result:
                results["questions_asked"] = 1
                results["topics_covered"].append(question.category)
                self.daily_grok_questions += 1
                self.last_grok_question_time = datetime.now()
                
                # Save the question to Notion for tracking
                await self._save_grok_interaction(question, question_result)
                
                # Wait for Grok to respond, then add our follow-up insight
                if question.follow_up_ready:
                    self.logger.info("Scheduling follow-up insight for later...")
                    # Note: In practice, you'd want to monitor for Grok's response
                    # and then add a follow-up. For now, we'll just log the intent.
                    results["follow_ups_posted"] = 1
            
            self.logger.info(f"✅ Grok engagement farming completed: {results['questions_asked']} questions asked")
            
        except Exception as e:
            self.logger.error(f"❌ Grok engagement farming failed: {e}")
            results["errors"].append(f"grok_farming: {e}")
        
        return results

    def _can_ask_grok_question(self) -> bool:
        """Check if we can ask another Grok question"""
        
        # Check daily limit
        if self.daily_grok_questions >= self.daily_grok_limit:
            return False
        
        # Check time interval
        if self.last_grok_question_time:
            time_since_last = datetime.now() - self.last_grok_question_time
            if time_since_last.total_seconds() < self.min_interval_between_questions:
                return False
        
        return True

    async def _select_strategic_question(self) -> Optional[GrokQuestion]:
        """Generate a strategic question using AI with deep business intelligence"""

        # Generate AI-powered question with full business context
        return await self._generate_strategic_ai_question()

    async def _generate_strategic_ai_question(self) -> Optional[GrokQuestion]:
        """Generate a strategic question using AI with deep business intelligence and market awareness"""

        try:
            # Get current business context
            current_hour = datetime.now().hour
            current_day = datetime.now().strftime("%A")
            current_month = datetime.now().strftime("%B")

            # Determine strategic focus based on timing and business cycles
            if 6 <= current_hour < 12:
                business_phase = "morning_operations"
                focus_area = "operational efficiency and daily planning"
                target_audience = "restaurant owners and managers starting their day"
            elif 12 <= current_hour < 17:
                business_phase = "midday_performance"
                focus_area = "real-time performance analysis and optimization"
                target_audience = "business decision makers monitoring performance"
            else:
                business_phase = "evening_strategy"
                focus_area = "strategic planning and growth optimization"
                target_audience = "business leaders and analysts planning ahead"

            # Choose engagement style for maximum viral potential
            engagement_styles = [
                "viral_challenge",
                "thought_leadership",
                "collaborative_insight",
                "industry_disruption",
                "data_revelation"
            ]

            style = random.choice(engagement_styles)

            # Create comprehensive AI prompt for strategic question generation
            prompt = f"""
            You are a strategic business influencer for SME Analytica, an AI-driven analytics platform that makes enterprise-level business intelligence accessible to SMEs (restaurants, hotels, retail).

            COMPANY CONTEXT:
            {self.sme_context}

            CURRENT BUSINESS CONTEXT:
            - Time: {current_hour}:00 on {current_day} in {current_month}
            - Business Phase: {business_phase}
            - Focus Area: {focus_area}
            - Target Audience: {target_audience}

            ENGAGEMENT STYLE: {style}

            MISSION: Generate a strategic question to ask @grok on Twitter that will:
            1. Position SME Analytica as a thought leader in business analytics
            2. Generate viral engagement and meaningful business conversations
            3. Attract potential customers from our target markets
            4. Showcase our expertise in data-driven business optimization
            5. Create opportunities for lead generation and brand awareness

            STYLE GUIDELINES:
            - {style.replace('_', ' ').title()}: Be innovative, insightful, and engaging
            - Address real business pain points that SMEs face daily
            - Demonstrate deep understanding of industry challenges
            - Position SME Analytica as the solution provider
            - Use conversational tone that builds trust and authority
            - Include strategic business insights that showcase expertise

            QUESTION REQUIREMENTS:
            - Start with "@grok" to ensure proper engagement
            - Maximum 280 characters total
            - Include relevant business hashtags (2-3 max)
            - Be authentic and valuable, not salesy
            - Encourage responses and viral sharing
            - Demonstrate thought leadership in business analytics

            Generate a strategic question that a true business influencer would ask to drive meaningful engagement and position SME Analytica as the go-to analytics solution for SMEs.
            """

            # Create content request for AI generation
            content_request = ContentRequest(
                content_type=ContentType.TWEET,
                language='en',
                theme=f'grok_strategic_question_{style}',
                max_length=280,
                context={
                    'prompt': prompt,
                    'business_phase': business_phase,
                    'target_audience': target_audience,
                    'engagement_style': style,
                    'sme_context': self.sme_context
                }
            )

            # Generate the question
            generated_content = await self.ai_manager.generate_content(content_request)

            if generated_content and generated_content.text:
                question_text = generated_content.text.strip()

                # Clean up the question (remove quotes if AI added them)
                if question_text.startswith('"') and question_text.endswith('"'):
                    question_text = question_text[1:-1]

                # Add @grok mention if not present
                if '@grok' not in question_text.lower():
                    question_text = f"@grok {question_text}"

                # Extract relevant hashtags based on content
                relevant_hashtags = self._extract_relevant_hashtags(question_text, style)

                # Create GrokQuestion object
                return GrokQuestion(
                    question=question_text,
                    category=f"ai_generated_{style}",
                    expected_engagement="high",  # AI-generated questions are expected to be high quality
                    follow_up_ready=True,
                    hashtags=relevant_hashtags,
                    target_audience=target_audience
                )

        except Exception as e:
            self.logger.error(f"Error generating strategic AI question: {e}")

        # Fallback: return None if AI generation fails
        return None

    def _extract_relevant_hashtags(self, question_text: str, style: str) -> List[str]:
        """Extract relevant hashtags based on question content and style"""

        # Base hashtags for SME Analytica
        base_hashtags = ["#SMEAnalytica", "#BusinessIntelligence", "#DataDriven"]

        # Style-specific hashtags
        style_hashtags = {
            "viral_challenge": ["#SMEAnalytica", "#BusinessChallenge", "#SMEGrowth", "#Innovation"],
            "thought_leadership": ["#SMEAnalytica", "#BusinessStrategy", "#Analytics", "#Leadership"],
            "collaborative_insight": ["#SMEAnalytica", "#BusinessInsights", "#Collaboration", "#SMESuccess"],
            "industry_disruption": ["#SMEAnalytica", "#Disruption", "#TechInnovation", "#FutureOfBusiness"],
            "data_revelation": ["#SMEAnalytica", "#DataInsights", "#BusinessOptimization", "#SmartBusiness"]
        }

        # Content-based hashtags
        content_hashtags = []
        question_lower = question_text.lower()

        if any(word in question_lower for word in ["restaurant", "menu", "food", "dining"]):
            content_hashtags.extend(["#RestaurantTech", "#MenuOptimization"])
        if any(word in question_lower for word in ["hotel", "hospitality", "guest"]):
            content_hashtags.extend(["#HospitalityTech", "#GuestExperience"])
        if any(word in question_lower for word in ["retail", "store", "inventory"]):
            content_hashtags.extend(["#RetailTech", "#InventoryManagement"])
        if any(word in question_lower for word in ["pricing", "price", "margin"]):
            content_hashtags.extend(["#DynamicPricing", "#ProfitOptimization"])

        # Combine and return top 3
        all_hashtags = base_hashtags + style_hashtags.get(style, []) + content_hashtags
        return list(dict.fromkeys(all_hashtags))[:3]  # Remove duplicates and limit to 3

    async def _ask_grok_question(self, question: GrokQuestion) -> Optional[str]:
        """Ask a strategic question to Grok"""
        
        try:
            # Format the question with hashtags
            formatted_question = f"{question.question}\n\n{' '.join(question.hashtags)}"
            
            # Post the question
            tweet_id = await self.twitter_manager.post_tweet(formatted_question)
            
            if tweet_id:
                self.logger.info(f"✅ Asked Grok question: {question.question[:50]}...")
                self.logger.info(f"Tweet ID: {tweet_id}")
                return tweet_id
            else:
                self.logger.error("Failed to post Grok question")
                return None
                
        except Exception as e:
            self.logger.error(f"Error asking Grok question: {e}")
            return None

    async def _save_grok_interaction(self, question: GrokQuestion, tweet_id: str):
        """Save Grok interaction to Notion for tracking"""
        
        try:
            # Create a post entry for the Grok question
            from ..notion.models import SocialMediaPost, PostStatus, Platform, PostType
            
            grok_post = SocialMediaPost(
                name=f"Grok Question - {question.category.title()} - {datetime.now().strftime('%Y-%m-%d')}",
                content=question.question,
                status=PostStatus.PUBLISHED,
                platform=Platform.TWITTER,
                post_type=PostType.ENGAGEMENT,
                published_time=datetime.now(),
                tweet_id=tweet_id,
                tags=question.hashtags,
                content_theme=f"grok_farming_{question.category}",
                ai_provider_used="grok_engagement_farming"
            )
            
            # Save to Notion
            notion_id = self.notion_manager.create_post(grok_post)
            if notion_id:
                self.logger.info("📊 Grok question saved to Notion database")
            else:
                self.logger.warning("⚠️ Failed to save Grok question to Notion")
                
        except Exception as e:
            self.logger.error(f"Error saving Grok interaction: {e}")

    async def generate_follow_up_insight(self, original_question: GrokQuestion, grok_response: str) -> Optional[str]:
        """Generate a follow-up insight based on Grok's response"""
        
        try:
            prompt = f"""
            Generate a professional follow-up tweet that adds valuable insight to this conversation.
            
            Original Question: "{original_question.question}"
            Grok's Response: "{grok_response}"
            
            Requirements:
            - Add genuine value from SME Analytica's perspective
            - Reference specific data or experience
            - Professional but conversational tone
            - Maximum 280 characters
            - Include 1-2 relevant emojis
            - Position SME Analytica as the expert
            
            Context: SME Analytica provides AI-driven analytics for restaurants, hotels, and retail businesses.
            """
            
            # Create content request
            content_request = ContentRequest(
                content_type=ContentType.TWEET,
                language='en',
                theme='engagement_follow_up',
                max_length=280,
                context={'prompt': prompt}
            )

            generated_content = await self.ai_manager.generate_content(content_request)
            follow_up = generated_content.text if generated_content else None
            
            if follow_up and len(follow_up.strip()) <= 280:
                return follow_up.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating follow-up insight: {e}")
        
        # Fallback follow-up templates
        fallback_insights = [
            f"Great points! In our restaurant analytics work, we've seen that {original_question.category.replace('_', ' ')} is often the key differentiator. 📊",
            f"This aligns with our data! We help SMEs implement exactly these kinds of insights without the enterprise-level complexity. 💡",
            f"Spot on! The challenge is making this actionable for small businesses. That's where focused analytics really shine. 🎯"
        ]
        
        return random.choice(fallback_insights)

    def get_grok_farming_stats(self) -> Dict[str, Any]:
        """Get Grok engagement farming statistics"""
        
        return {
            "daily_questions_asked": self.daily_grok_questions,
            "daily_limit": self.daily_grok_limit,
            "remaining_questions": self.daily_grok_limit - self.daily_grok_questions,
            "last_question_time": self.last_grok_question_time.isoformat() if self.last_grok_question_time else None,
            "can_ask_question": self._can_ask_grok_question(),
            "available_categories": list(self.question_categories.keys()),
            "total_strategic_questions": len(self.strategic_questions)
        }
