logs/
data/*.db
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.env
.venv
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Additional Security - API Keys and Secrets
*api_key*
*secret*
*token*
*password*
credentials.json
config.json

# Database Files
*.db
*.sqlite
*.sqlite3
data/
analytics_data/
automation_logs/

# Test files (removed from repo)
test_*
*_test.py
tests/
