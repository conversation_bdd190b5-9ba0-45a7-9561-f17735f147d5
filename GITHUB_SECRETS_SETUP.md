# 🔐 GitHub Secrets Setup Guide

To run your SME Social Media Manager in the cloud via GitHub Actions, you need to add your API keys as GitHub Secrets.

## 🚀 Quick Setup Steps

### 1. Go to Your Repository Settings
1. Open your repository: https://github.com/Ola-Yeenca/sme-social-media-manager
2. Click on **Settings** tab
3. In the left sidebar, click **Secrets and variables** → **Actions**
4. Click **New repository secret**

### 2. Add Required Secrets

Add each of these secrets one by one:

#### **Twitter API Credentials** (Required)
- **Name**: `TWITTER_API_KEY`
- **Value**: Your Twitter API key from `.env` file

- **Name**: `TWITTER_API_SECRET`
- **Value**: Your Twitter API secret from `.env` file

- **Name**: `TWITTER_ACCESS_TOKEN`
- **Value**: Your Twitter access token from `.env` file

- **Name**: `TWITTER_ACCESS_TOKEN_SECRET`
- **Value**: Your Twitter access token secret from `.env` file

- **Name**: `TWITTER_BEARER_TOKEN`
- **Value**: Your Twitter bearer token from `.env` file

#### **Notion Credentials** (Required)
- **Name**: `NOTION_API_KEY`
- **Value**: Your Notion API key from `.env` file

- **Name**: `SOCIAL_MEDIA_DB_ID`
- **Value**: Your Notion database ID from `.env` file

#### **AI Provider Credentials** (Optional)
- **Name**: `OPENAI_API_KEY`
- **Value**: Your OpenAI API key (if you have one)

- **Name**: `ANTHROPIC_API_KEY`
- **Value**: Your Anthropic API key (if you have one)

- **Name**: `GROK_API_KEY`
- **Value**: Your Grok API key (if you have one)

- **Name**: `PERPLEXITY_API_KEY`
- **Value**: Your Perplexity API key (if you have one)

## 📋 Copy Values from Your .env File

Your `.env` file contains all the values you need. Here's how to copy them:

```bash
# View your current .env file (DO NOT share these values!)
cat .env
```

Copy each value after the `=` sign and paste it into the corresponding GitHub Secret.

## ⚠️ Important Security Notes

1. **Never commit your `.env` file** - it's already in `.gitignore`
2. **GitHub Secrets are encrypted** and only accessible to your repository
3. **Don't share these values** with anyone
4. **Each secret should be added individually** - don't copy the whole `.env` file

## 🔄 Automation Schedule

Once secrets are set up, your automation will run:

### **Automatic Schedule:**
- **Daily at 8:00 AM UTC** - Full automation (content generation + posting + growth)
- **Every 6 hours** - Posting and engagement activities

### **Manual Triggers:**
You can also run automation manually:
1. Go to **Actions** tab in your repository
2. Click **SME Analytica Social Media Automation**
3. Click **Run workflow**
4. Choose the mode:
   - `full` - Complete automation
   - `post` - Just posting
   - `grow` - Just growth activities
   - `content` - Just content generation
   - `analytics` - Just analytics

## 📊 Monitoring Your Automation

### **Check Automation Status:**
1. Go to **Actions** tab in your repository
2. View recent workflow runs
3. Click on any run to see detailed logs

### **Download Analytics:**
1. In the **Actions** tab, click on a completed run
2. Scroll down to **Artifacts**
3. Download `automation-logs-XXX` to see performance data

## 🎯 Expected Results

Once set up, your automation will:
- ✅ **Run 24/7** regardless of your laptop status
- ✅ **Generate 6-12 posts daily** with strategic content
- ✅ **Engage with target audience** automatically
- ✅ **Track analytics** and optimize performance
- ✅ **Grow from 8 to 1,000+ followers** systematically

## 🔧 Troubleshooting

### **If automation fails:**
1. Check the **Actions** tab for error messages
2. Verify all secrets are correctly set
3. Make sure secret names match exactly (case-sensitive)

### **If you need to update secrets:**
1. Go to **Settings** → **Secrets and variables** → **Actions**
2. Click on the secret name
3. Click **Update** and enter the new value

### **If you want to change the schedule:**
1. Edit `.github/workflows/social-media-automation.yml`
2. Modify the `cron` values under `schedule:`
3. Commit and push the changes

## 🚀 Ready to Go!

After adding all secrets:
1. **Push your changes** to GitHub (the workflow file)
2. **Check the Actions tab** to see if automation starts
3. **Monitor the first few runs** to ensure everything works
4. **Watch your follower count grow!** 📈

Your SME Analytica social media will now grow automatically in the cloud! 🎉
